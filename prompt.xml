update the code, improve performance, accessibility, maintainable, error handling, add memoization where possible if there are any heavy computation, add meaninful comments, respect reduce motion when animating import useReducedMotion from "framer-motion", and for best practices. Make sure no to break any functionalities and layout setup. Extract any values possible to constants, just keep them in the same file if just a few lines. create a hook file if there is a chance to do so. Always follow Next.js best practices, linting errors, and type errors. Always double check for responsiveness for mobile device. Whenever proposing a new or updated file use the markdown code block syntax and always add a file path in a comment on the top of the code block. Please show me the full code of the changed files, I have a disability which means I can't type and need to be able to copy and paste the full code. Don't use XML for file.
<file path="src/components/home/<USER>">
    <![CDATA[ // src/components/home/<USER>
"use client";

import { useRef, ReactNode } from "react";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { useReducedMotion } from "framer-motion";

interface AnimatedSectionProps {
    children: ReactNode;
    className?: string;
}

const AnimatedSection = ({
    children,
    className,
}: AnimatedSectionProps): JSX.Element => {
    const sectionRef = useRef<HTMLDivElement>(null);
    const prefersReducedMotion = useReducedMotion();

    // On mobile, animate once. On desktop, animate every time it enters the viewport.
    const isVisible = useIntersectionObserver(sectionRef, {
        triggerOnce: useIsMobile(),
        threshold: 0.2,
    });

    return (
        <div
            ref={sectionRef}
            className={cn(
                "transition-all duration-700 ease-out",
                "will-change-opacity-transform",
                isVisible
                    ? "opacity-100 translate-y-0"
                    : "opacity-10 translate-y-12",
                className,
            )}
        >
            {children}
        </div>
    );
};

export default AnimatedSection;

  ]]>
</file>