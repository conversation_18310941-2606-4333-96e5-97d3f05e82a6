// src/app/theme-switch-demo/page.tsx
"use client";

import * as React from "react";
import { ThemeSwitch } from "@/components/ui/theme-switch";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";

export default function ThemeSwitchDemo() {
    const [theme, setTheme] = React.useState<"light" | "dark">("light");
    const [controlledTheme, setControlledTheme] = React.useState(false);
    const [disabledState, setDisabledState] = React.useState(false);

    const handleThemeChange = React.useCallback((checked: boolean) => {
        setTheme(checked ? "dark" : "light");
        console.log("Theme changed to:", checked ? "dark" : "light");
    }, []);

    const handleControlledChange = React.useCallback((checked: boolean) => {
        setControlledTheme(checked);
        console.log("Controlled theme changed to:", checked ? "dark" : "light");
    }, []);

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
            <div className="max-w-6xl mx-auto px-4 py-8 space-y-12">
                {/* Header */}
                <div className="text-center space-y-4">
                    <h1 className="text-4xl font-bold text-foreground">
                        ThemeSwitch Component Demo
                    </h1>
                    <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        A beautiful 3D animated theme toggle switch with smooth
                        sun/moon transitions, animated clouds, stars, and full
                        accessibility support.
                    </p>
                    <div className="flex flex-wrap justify-center gap-2">
                        <Badge variant="secondary">3D Animations</Badge>
                        <Badge variant="secondary">Accessibility</Badge>
                        <Badge variant="secondary">TypeScript</Badge>
                        <Badge variant="secondary">Responsive</Badge>
                        <Badge variant="secondary">Keyboard Navigation</Badge>
                    </div>
                </div>

                {/* Sizes Section */}
                <section className="space-y-6">
                    <h2 className="text-2xl font-semibold text-foreground">
                        Switch Sizes
                    </h2>
                    <p className="text-muted-foreground">
                        Available in three different sizes to fit various use
                        cases and layouts.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center justify-items-center">
                        <div className="text-center space-y-3">
                            <ThemeSwitch size="sm" defaultChecked={false} />
                            <div>
                                <p className="font-medium">Small</p>
                                <p className="text-sm text-muted-foreground">
                                    20px base size
                                </p>
                            </div>
                        </div>
                        <div className="text-center space-y-3">
                            <ThemeSwitch
                                size="default"
                                defaultChecked={false}
                            />
                            <div>
                                <p className="font-medium">Default</p>
                                <p className="text-sm text-muted-foreground">
                                    30px base size
                                </p>
                            </div>
                        </div>
                        <div className="text-center space-y-3">
                            <ThemeSwitch size="lg" defaultChecked={false} />
                            <div>
                                <p className="font-medium">Large</p>
                                <p className="text-sm text-muted-foreground">
                                    40px base size
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section className="space-y-6">
                    <h2 className="text-2xl font-semibold text-foreground">
                        Features
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">
                                    Uncontrolled Switch
                                </CardTitle>
                                <CardDescription>
                                    Manages its own state internally
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="flex justify-center">
                                <ThemeSwitch
                                    defaultChecked={false}
                                    onChange={handleThemeChange}
                                />
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">
                                    Controlled Switch
                                </CardTitle>
                                <CardDescription>
                                    State managed by parent component
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="flex justify-center">
                                <ThemeSwitch
                                    checked={controlledTheme}
                                    onChange={handleControlledChange}
                                />
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">
                                    Disabled State
                                </CardTitle>
                                <CardDescription>
                                    Non-interactive disabled switch
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="flex justify-center">
                                <ThemeSwitch
                                    checked={true}
                                    disabled={true}
                                    aria-label="Disabled theme switch"
                                />
                            </CardContent>
                        </Card>
                    </div>
                </section>

                {/* Interactive Controls */}
                <section className="space-y-6">
                    <h2 className="text-2xl font-semibold text-foreground">
                        Interactive Controls
                    </h2>
                    <p className="text-muted-foreground">
                        Test different states and configurations of the theme
                        switch.
                    </p>
                    <Card>
                        <CardHeader>
                            <CardTitle>Live Demo</CardTitle>
                            <CardDescription>
                                Current theme: <strong>{theme}</strong> |
                                Controlled state:{" "}
                                <strong>
                                    {controlledTheme ? "dark" : "light"}
                                </strong>
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="flex justify-center">
                                <ThemeSwitch
                                    checked={controlledTheme}
                                    onChange={handleControlledChange}
                                    disabled={disabledState}
                                    size="lg"
                                />
                            </div>
                            <div className="flex flex-wrap gap-4 justify-center">
                                <Button
                                    variant="outline"
                                    onClick={() =>
                                        setControlledTheme(!controlledTheme)
                                    }
                                >
                                    Toggle Theme
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={() =>
                                        setDisabledState(!disabledState)
                                    }
                                >
                                    {disabledState ? "Enable" : "Disable"}{" "}
                                    Switch
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </section>

                {/* Accessibility Section */}
                <section className="space-y-6">
                    <h2 className="text-2xl font-semibold text-foreground">
                        Accessibility Features
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">
                                    Keyboard Navigation
                                </CardTitle>
                                <CardDescription>
                                    Focus and activate with keyboard
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="flex justify-center">
                                <ThemeSwitch
                                    aria-label="Keyboard accessible theme switch"
                                    defaultChecked={false}
                                />
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">
                                    Screen Reader Support
                                </CardTitle>
                                <CardDescription>
                                    Proper ARIA labels and roles
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="flex justify-center">
                                <ThemeSwitch
                                    aria-label="Theme toggle for dark and light modes"
                                    defaultChecked={true}
                                />
                            </CardContent>
                        </Card>
                    </div>
                </section>

                {/* Code Examples Section */}
                <section className="space-y-6">
                    <h2 className="text-2xl font-semibold text-foreground">
                        Code Examples
                    </h2>
                    <p className="text-muted-foreground">
                        Copy and paste these examples to get started with the
                        ThemeSwitch component.
                    </p>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Basic Usage */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">
                                    Basic Usage
                                </CardTitle>
                                <CardDescription>
                                    Simple uncontrolled theme switch
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex justify-center">
                                    <ThemeSwitch defaultChecked={false} />
                                </div>
                                <pre className="bg-muted p-4 rounded-md text-sm overflow-x-auto">
                                    <code>{`import { ThemeSwitch } from "@/components/ui/theme-switch";

<ThemeSwitch defaultChecked={false} />`}</code>
                                </pre>
                            </CardContent>
                        </Card>

                        {/* Controlled Usage */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">
                                    Controlled Usage
                                </CardTitle>
                                <CardDescription>
                                    Theme switch with external state management
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex justify-center">
                                    <ThemeSwitch
                                        checked={controlledTheme}
                                        onChange={handleControlledChange}
                                    />
                                </div>
                                <pre className="bg-muted p-4 rounded-md text-sm overflow-x-auto">
                                    <code>{`const [isDark, setIsDark] = useState(false);

<ThemeSwitch
  checked={isDark}
  onChange={(checked) => setIsDark(checked)}
/>`}</code>
                                </pre>
                            </CardContent>
                        </Card>

                        {/* Size Variants */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">
                                    Size Variants
                                </CardTitle>
                                <CardDescription>
                                    Different sizes for various use cases
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex justify-center gap-4">
                                    <ThemeSwitch
                                        size="sm"
                                        defaultChecked={false}
                                    />
                                    <ThemeSwitch
                                        size="default"
                                        defaultChecked={false}
                                    />
                                    <ThemeSwitch
                                        size="lg"
                                        defaultChecked={false}
                                    />
                                </div>
                                <pre className="bg-muted p-4 rounded-md text-sm overflow-x-auto">
                                    <code>{`<ThemeSwitch size="sm" />
<ThemeSwitch size="default" />
<ThemeSwitch size="lg" />`}</code>
                                </pre>
                            </CardContent>
                        </Card>

                        {/* With Theme Provider */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">
                                    Theme Provider Integration
                                </CardTitle>
                                <CardDescription>
                                    Integration with theme context providers
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex justify-center">
                                    <ThemeSwitch
                                        checked={theme === "dark"}
                                        onChange={(checked) =>
                                            setTheme(checked ? "dark" : "light")
                                        }
                                    />
                                </div>
                                <pre className="bg-muted p-4 rounded-md text-sm overflow-x-auto">
                                    <code>{`import { useTheme } from "next-themes";

function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  return (
    <ThemeSwitch
      checked={theme === "dark"}
      onChange={(checked) =>
        setTheme(checked ? "dark" : "light")
      }
    />
  );
}`}</code>
                                </pre>
                            </CardContent>
                        </Card>
                    </div>
                </section>

                {/* Advanced Usage */}
                <section className="space-y-6">
                    <h2 className="text-2xl font-semibold text-foreground">
                        Advanced Usage
                    </h2>
                    <Card>
                        <CardHeader>
                            <CardTitle>Custom Hook Integration</CardTitle>
                            <CardDescription>
                                Example of creating a custom hook for theme
                                management
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <pre className="bg-muted p-4 rounded-md text-sm overflow-x-auto">
                                <code>{`import { useState, useEffect } from "react";
import { ThemeSwitch } from "@/components/ui/theme-switch";

function useThemeSwitch(defaultTheme = "light") {
  const [theme, setTheme] = useState(defaultTheme);

  useEffect(() => {
    document.documentElement.classList.toggle("dark", theme === "dark");
  }, [theme]);

  const toggleTheme = (checked: boolean) => {
    setTheme(checked ? "dark" : "light");
  };

  return { theme, toggleTheme };
}

function App() {
  const { theme, toggleTheme } = useThemeSwitch();

  return (
    <ThemeSwitch
      checked={theme === "dark"}
      onChange={toggleTheme}
      aria-label="Toggle dark mode"
    />
  );
}`}</code>
                            </pre>
                        </CardContent>
                    </Card>
                </section>
            </div>
        </div>
    );
}
