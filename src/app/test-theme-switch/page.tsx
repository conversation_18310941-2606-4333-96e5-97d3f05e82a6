// src/app/test-theme-switch/page.tsx
"use client";

import { ThemeSwitch } from "@/components/ui/theme-switch";

export default function TestThemeSwitch() {
    return (
        <div className="min-h-screen flex items-center justify-center bg-background">
            <div className="space-y-8 text-center">
                <h1 className="text-2xl font-bold">Theme Switch Test</h1>
                <div className="space-y-4">
                    <div>
                        <p className="mb-2">Small</p>
                        <ThemeSwitch size="sm" />
                    </div>
                    <div>
                        <p className="mb-2">Default</p>
                        <ThemeSwitch size="default" />
                    </div>
                    <div>
                        <p className="mb-2">Large</p>
                        <ThemeSwitch size="lg" />
                    </div>
                </div>
            </div>
        </div>
    );
}
