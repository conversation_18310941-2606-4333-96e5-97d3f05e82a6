// src/components/ui/theme-switch.tsx
"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";

// Component variants for different sizes
const themeSwitchVariants = cva("theme-switch", {
    variants: {
        size: {
            sm: "theme-switch--sm",
            default: "theme-switch--default",
            lg: "theme-switch--lg",
        },
    },
    defaultVariants: {
        size: "default",
    },
});

// Size configurations
const SWITCH_SIZES = {
    sm: { toggleSize: "20px" },
    default: { toggleSize: "30px" },
    lg: { toggleSize: "40px" },
} as const;

export interface ThemeSwitchProps
    extends Omit<
            React.InputHTMLAttributes<HTMLInputElement>,
            "size" | "onChange"
        >,
        VariantProps<typeof themeSwitchVariants> {
    /**
     * Whether the switch is checked (dark mode)
     */
    checked?: boolean;
    /**
     * De<PERSON>ult checked state for uncontrolled component
     */
    defaultChecked?: boolean;
    /**
     * Callback fired when the switch state changes
     */
    onChange?: (
        checked: boolean,
        event: React.ChangeEvent<HTMLInputElement>,
    ) => void;
    /**
     * Whether the switch is disabled
     */
    disabled?: boolean;
    /**
     * Custom class name
     */
    className?: string;
    /**
     * Accessible label for the switch
     */
    "aria-label"?: string;
    /**
     * Additional aria-labelledby reference
     */
    "aria-labelledby"?: string;
}

// Generate CSS styles for the theme switch
const getThemeSwitchStyles = (
    size: keyof typeof SWITCH_SIZES,
    disabled: boolean,
) => {
    const config = SWITCH_SIZES[size];

    return `
    .theme-switch--${size} {
        --toggle-size: ${config.toggleSize};
        /* the size is adjusted using font-size,
           this is not transform scale,
           so you can choose any size */
        --container-width: 5.625em;
        --container-height: 2.5em;
        --container-radius: 6.25em;
        /* radius 0 - minecraft mode :) */
        --container-light-bg: #3D7EAE;
        --container-night-bg: #1D1F2C;
        --circle-container-diameter: 3.375em;
        --sun-moon-diameter: 2.125em;
        --sun-bg: #ECCA2F;
        --moon-bg: #C4C9D1;
        --spot-color: #959DB1;
        --circle-container-offset: calc((var(--circle-container-diameter) - var(--container-height)) / 2 * -1);
        --stars-color: #fff;
        --clouds-color: #F3FDFF;
        --back-clouds-color: #AACADF;
        --transition: .5s cubic-bezier(0, -0.02, 0.4, 1.25);
        --circle-transition: .3s cubic-bezier(0, -0.02, 0.35, 1.17);
    }

    .theme-switch--${size}, 
    .theme-switch--${size} *, 
    .theme-switch--${size} *::before, 
    .theme-switch--${size} *::after {
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        font-size: var(--toggle-size);
    }

    .theme-switch--${size} .theme-switch__container {
        width: var(--container-width);
        height: var(--container-height);
        background-color: var(--container-light-bg);
        border-radius: var(--container-radius);
        overflow: hidden;
        cursor: ${disabled ? "not-allowed" : "pointer"};
        -webkit-box-shadow: 0em -0.062em 0.062em rgba(0, 0, 0, 0.25), 0em 0.062em 0.125em rgba(255, 255, 255, 0.94);
        box-shadow: 0em -0.062em 0.062em rgba(0, 0, 0, 0.25), 0em 0.062em 0.125em rgba(255, 255, 255, 0.94);
        -webkit-transition: var(--transition);
        -o-transition: var(--transition);
        transition: var(--transition);
        position: relative;
        opacity: ${disabled ? "0.5" : "1"};
    }

    .theme-switch--${size} .theme-switch__container::before {
        content: "";
        position: absolute;
        z-index: 1;
        inset: 0;
        -webkit-box-shadow: 0em 0.05em 0.187em rgba(0, 0, 0, 0.25) inset, 0em 0.05em 0.187em rgba(0, 0, 0, 0.25) inset;
        box-shadow: 0em 0.05em 0.187em rgba(0, 0, 0, 0.25) inset, 0em 0.05em 0.187em rgba(0, 0, 0, 0.25) inset;
        border-radius: var(--container-radius);
    }

    .theme-switch--${size} .theme-switch__checkbox {
        display: none;
    }

    .theme-switch--${size} .theme-switch__circle-container {
        width: var(--circle-container-diameter);
        height: var(--circle-container-diameter);
        background-color: rgba(255, 255, 255, 0.1);
        position: absolute;
        left: var(--circle-container-offset);
        top: var(--circle-container-offset);
        border-radius: var(--container-radius);
        -webkit-box-shadow: inset 0 0 0 3.375em rgba(255, 255, 255, 0.1), inset 0 0 0 3.375em rgba(255, 255, 255, 0.1), 0 0 0 0.625em rgba(255, 255, 255, 0.1), 0 0 0 1.25em rgba(255, 255, 255, 0.1);
        box-shadow: inset 0 0 0 3.375em rgba(255, 255, 255, 0.1), inset 0 0 0 3.375em rgba(255, 255, 255, 0.1), 0 0 0 0.625em rgba(255, 255, 255, 0.1), 0 0 0 1.25em rgba(255, 255, 255, 0.1);
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-transition: var(--circle-transition);
        -o-transition: var(--circle-transition);
        transition: var(--circle-transition);
        pointer-events: none;
    }

    .theme-switch--${size} .theme-switch__sun-moon-container {
        pointer-events: auto;
        position: relative;
        z-index: 2;
        width: var(--sun-moon-diameter);
        height: var(--sun-moon-diameter);
        margin: auto;
        border-radius: var(--container-radius);
        background-color: var(--sun-bg);
        -webkit-box-shadow: 0.062em 0.062em 0.062em 0em rgba(254, 255, 239, 0.61) inset, 0em -0.062em 0.062em 0em #a1872a inset;
        box-shadow: 0.062em 0.062em 0.062em 0em rgba(254, 255, 239, 0.61) inset, 0em -0.062em 0.062em 0em #a1872a inset;
        -webkit-filter: drop-shadow(0.062em 0.125em 0.125em rgba(0, 0, 0, 0.25)) drop-shadow(0em 0.062em 0.125em rgba(0, 0, 0, 0.25));
        filter: drop-shadow(0.062em 0.125em 0.125em rgba(0, 0, 0, 0.25)) drop-shadow(0em 0.062em 0.125em rgba(0, 0, 0, 0.25));
        overflow: hidden;
        -webkit-transition: var(--transition);
        -o-transition: var(--transition);
        transition: var(--transition);
    }

    .theme-switch--${size} .theme-switch__moon {
        -webkit-transform: translateX(100%);
        -ms-transform: translateX(100%);
        transform: translateX(100%);
        width: 100%;
        height: 100%;
        background-color: var(--moon-bg);
        border-radius: inherit;
        -webkit-box-shadow: 0.062em 0.062em 0.062em 0em rgba(254, 255, 239, 0.61) inset, 0em -0.062em 0.062em 0em #969696 inset;
        box-shadow: 0.062em 0.062em 0.062em 0em rgba(254, 255, 239, 0.61) inset, 0em -0.062em 0.062em 0em #969696 inset;
        -webkit-transition: var(--transition);
        -o-transition: var(--transition);
        transition: var(--transition);
        position: relative;
    }

    .theme-switch--${size} .theme-switch__spot {
        position: absolute;
        top: 0.75em;
        left: 0.312em;
        width: 0.75em;
        height: 0.75em;
        border-radius: var(--container-radius);
        background-color: var(--spot-color);
        -webkit-box-shadow: 0em 0.0312em 0.062em rgba(0, 0, 0, 0.25) inset;
        box-shadow: 0em 0.0312em 0.062em rgba(0, 0, 0, 0.25) inset;
    }

    .theme-switch--${size} .theme-switch__spot:nth-of-type(2) {
        width: 0.375em;
        height: 0.375em;
        top: 0.937em;
        left: 1.375em;
    }

    .theme-switch--${size} .theme-switch__spot:nth-last-of-type(3) {
        width: 0.25em;
        height: 0.25em;
        top: 0.312em;
        left: 0.812em;
    }

    .theme-switch--${size} .theme-switch__clouds {
        width: 1.25em;
        height: 1.25em;
        background-color: var(--clouds-color);
        border-radius: var(--container-radius);
        position: absolute;
        bottom: -0.625em;
        left: 0.312em;
        -webkit-box-shadow: 0.937em 0.312em var(--clouds-color), -0.312em -0.312em var(--back-clouds-color), 1.437em 0.375em var(--clouds-color), 0.5em -0.125em var(--back-clouds-color), 2.187em 0 var(--clouds-color), 1.25em -0.062em var(--back-clouds-color), 2.937em 0.312em var(--clouds-color), 2em -0.312em var(--back-clouds-color), 3.625em -0.062em var(--clouds-color), 2.625em 0em var(--back-clouds-color), 4.5em -0.312em var(--clouds-color), 3.375em -0.437em var(--back-clouds-color), 4.625em -1.75em 0 0.437em var(--clouds-color), 4em -0.625em var(--back-clouds-color), 4.125em -2.125em 0 0.437em var(--back-clouds-color);
        box-shadow: 0.937em 0.312em var(--clouds-color), -0.312em -0.312em var(--back-clouds-color), 1.437em 0.375em var(--clouds-color), 0.5em -0.125em var(--back-clouds-color), 2.187em 0 var(--clouds-color), 1.25em -0.062em var(--back-clouds-color), 2.937em 0.312em var(--clouds-color), 2em -0.312em var(--back-clouds-color), 3.625em -0.062em var(--clouds-color), 2.625em 0em var(--back-clouds-color), 4.5em -0.312em var(--clouds-color), 3.375em -0.437em var(--back-clouds-color), 4.625em -1.75em 0 0.437em var(--clouds-color), 4em -0.625em var(--back-clouds-color), 4.125em -2.125em 0 0.437em var(--back-clouds-color);
        -webkit-transition: 0.5s cubic-bezier(0, -0.02, 0.4, 1.25);
        -o-transition: 0.5s cubic-bezier(0, -0.02, 0.4, 1.25);
        transition: 0.5s cubic-bezier(0, -0.02, 0.4, 1.25);
    }

    .theme-switch--${size} .theme-switch__stars-container {
        position: absolute;
        color: var(--stars-color);
        top: -100%;
        left: 0.312em;
        width: 2.75em;
        height: auto;
        -webkit-transition: var(--transition);
        -o-transition: var(--transition);
        transition: var(--transition);
    }

    /* Interactive states and actions */
    .theme-switch--${size} .theme-switch__checkbox:checked + .theme-switch__container {
        background-color: var(--container-night-bg);
    }

    .theme-switch--${size} .theme-switch__checkbox:checked + .theme-switch__container .theme-switch__circle-container {
        left: calc(100% - var(--circle-container-offset) - var(--circle-container-diameter));
    }

    .theme-switch--${size} .theme-switch__checkbox:checked + .theme-switch__container .theme-switch__circle-container:hover {
        left: calc(100% - var(--circle-container-offset) - var(--circle-container-diameter) - 0.187em);
    }

    .theme-switch--${size} .theme-switch__circle-container:hover {
        left: calc(var(--circle-container-offset) + 0.187em);
    }

    .theme-switch--${size} .theme-switch__checkbox:checked + .theme-switch__container .theme-switch__moon {
        -webkit-transform: translate(0);
        -ms-transform: translate(0);
        transform: translate(0);
    }

    .theme-switch--${size} .theme-switch__checkbox:checked + .theme-switch__container .theme-switch__clouds {
        bottom: -4.062em;
    }

    .theme-switch--${size} .theme-switch__checkbox:checked + .theme-switch__container .theme-switch__stars-container {
        top: 50%;
        -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }

    /* Focus states for accessibility */
    .theme-switch--${size} .theme-switch__checkbox:focus + .theme-switch__container {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
    }

    /* Disabled state */
    .theme-switch--${size} .theme-switch__checkbox:disabled + .theme-switch__container {
        cursor: not-allowed;
        opacity: 0.5;
    }

    .theme-switch--${size} .theme-switch__checkbox:disabled + .theme-switch__container .theme-switch__circle-container:hover {
        left: var(--circle-container-offset);
    }

    .theme-switch--${size} .theme-switch__checkbox:disabled:checked + .theme-switch__container .theme-switch__circle-container:hover {
        left: calc(100% - var(--circle-container-offset) - var(--circle-container-diameter));
    }
    `;
};

// Stars SVG component for the night mode
const StarsIcon = React.memo(() => (
    <svg
        width="2.75em"
        height="1.5em"
        viewBox="0 0 44 24"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
    >
        <circle cx="6" cy="6" r="1" />
        <circle cx="12" cy="4" r="0.5" />
        <circle cx="18" cy="8" r="0.8" />
        <circle cx="24" cy="3" r="0.6" />
        <circle cx="30" cy="7" r="1" />
        <circle cx="36" cy="5" r="0.7" />
        <circle cx="9" cy="12" r="0.5" />
        <circle cx="15" cy="14" r="0.8" />
        <circle cx="21" cy="11" r="0.6" />
        <circle cx="27" cy="15" r="0.5" />
        <circle cx="33" cy="13" r="0.7" />
        <circle cx="39" cy="16" r="0.6" />
        <circle cx="3" cy="18" r="0.8" />
        <circle cx="42" cy="20" r="0.5" />
    </svg>
));

StarsIcon.displayName = "StarsIcon";

/**
 * ThemeSwitch - A beautiful 3D animated theme toggle switch
 *
 * Features:
 * - Smooth sun/moon transition animations
 * - 3D visual effects with shadows and gradients
 * - Animated clouds and stars
 * - Multiple size variants
 * - Full accessibility support
 * - Keyboard navigation
 * - Controlled and uncontrolled modes
 */
const ThemeSwitch = React.memo(
    React.forwardRef<HTMLInputElement, ThemeSwitchProps>(
        (
            {
                size = "default",
                checked,
                defaultChecked,
                onChange,
                disabled = false,
                className,
                "aria-label": ariaLabel,
                "aria-labelledby": ariaLabelledBy,
                ...props
            },
            ref,
        ) => {
            // Handle controlled vs uncontrolled state
            const [internalChecked, setInternalChecked] = React.useState(
                defaultChecked ?? false,
            );

            const isControlled = checked !== undefined;
            const checkedValue = isControlled ? checked : internalChecked;

            // Generate unique ID for accessibility
            const switchId = React.useId();

            // Handle change events
            const handleChange = React.useCallback(
                (event: React.ChangeEvent<HTMLInputElement>) => {
                    const newChecked = event.target.checked;

                    if (!isControlled) {
                        setInternalChecked(newChecked);
                    }

                    onChange?.(newChecked, event);
                },
                [isControlled, onChange],
            );

            // Handle keyboard events for accessibility
            const handleKeyDown = React.useCallback(
                (event: React.KeyboardEvent<HTMLLabelElement>) => {
                    if (disabled) return;

                    if (event.key === " " || event.key === "Enter") {
                        event.preventDefault();
                        const checkbox = event.currentTarget.querySelector(
                            'input[type="checkbox"]',
                        ) as HTMLInputElement;
                        if (checkbox) {
                            checkbox.click();
                        }
                    }
                },
                [disabled],
            );

            // Generate styles for current size
            const switchStyles = React.useMemo(
                () => getThemeSwitchStyles(size, disabled),
                [size, disabled],
            );

            return (
                <label
                    className={cn(themeSwitchVariants({ size }), className)}
                    onKeyDown={handleKeyDown}
                    role="switch"
                    aria-checked={checkedValue}
                    aria-label={
                        ariaLabel ||
                        `Toggle ${checkedValue ? "light" : "dark"} theme`
                    }
                    aria-labelledby={ariaLabelledBy}
                    tabIndex={disabled ? -1 : 0}
                >
                    <style jsx>{switchStyles}</style>

                    <input
                        ref={ref}
                        id={switchId}
                        type="checkbox"
                        className="theme-switch__checkbox"
                        checked={checkedValue}
                        onChange={handleChange}
                        disabled={disabled}
                        aria-hidden="true"
                        tabIndex={-1}
                        {...props}
                    />

                    <div className="theme-switch__container">
                        <div className="theme-switch__circle-container">
                            <div className="theme-switch__sun-moon-container">
                                <div className="theme-switch__moon">
                                    {/* Moon spots */}
                                    <div className="theme-switch__spot" />
                                    <div className="theme-switch__spot" />
                                    <div className="theme-switch__spot" />
                                </div>
                            </div>
                        </div>

                        {/* Clouds */}
                        <div className="theme-switch__clouds" />

                        {/* Stars */}
                        <div className="theme-switch__stars-container">
                            <StarsIcon />
                        </div>
                    </div>
                </label>
            );
        },
    ),
);

ThemeSwitch.displayName = "ThemeSwitch";

export { ThemeSwitch, type ThemeSwitchProps };
export default ThemeSwitch;
