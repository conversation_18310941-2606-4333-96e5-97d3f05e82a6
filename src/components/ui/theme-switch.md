# ThemeSwitch Component

A beautiful 3D animated theme toggle switch component with smooth sun/moon transitions, animated clouds, stars, and full accessibility support.

## Features

- **3D Visual Effects**: Realistic shadows, gradients, and depth effects
- **Smooth Animations**: Fluid sun/moon transitions with animated clouds and stars
- **Multiple Sizes**: Three size variants (sm, default, lg) with proportional scaling
- **Full Accessibility**: ARIA labels, keyboard navigation, screen reader support
- **Controlled/Uncontrolled**: Supports both controlled and uncontrolled modes
- **TypeScript**: Full TypeScript support with comprehensive type definitions
- **Responsive**: Works perfectly on all device sizes
- **Performance**: Optimized with React.memo and efficient re-renders

## Installation

The component is already included in your project. Simply import it:

```tsx
import { ThemeSwitch } from "@/components/ui/theme-switch";
```

## Basic Usage

### Uncontrolled Component

```tsx
import { ThemeSwitch } from "@/components/ui/theme-switch";

export default function MyComponent() {
  return (
    <ThemeSwitch 
      defaultChecked={false}
      onChange={(checked) => console.log('Theme:', checked ? 'dark' : 'light')}
    />
  );
}
```

### Controlled Component

```tsx
import { useState } from "react";
import { ThemeSwitch } from "@/components/ui/theme-switch";

export default function MyComponent() {
  const [isDark, setIsDark] = useState(false);

  return (
    <ThemeSwitch
      checked={isDark}
      onChange={(checked) => setIsDark(checked)}
    />
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `size` | `"sm" \| "default" \| "lg"` | `"default"` | Size variant of the switch |
| `checked` | `boolean` | `undefined` | Controlled checked state |
| `defaultChecked` | `boolean` | `false` | Default checked state for uncontrolled mode |
| `onChange` | `(checked: boolean, event: ChangeEvent) => void` | `undefined` | Callback fired when state changes |
| `disabled` | `boolean` | `false` | Whether the switch is disabled |
| `className` | `string` | `undefined` | Additional CSS classes |
| `aria-label` | `string` | Auto-generated | Accessible label for the switch |
| `aria-labelledby` | `string` | `undefined` | Reference to labeling element |

## Size Variants

### Small (`size="sm"`)
- Base size: 20px
- Ideal for: Compact UIs, mobile interfaces, secondary controls

### Default (`size="default"`)
- Base size: 30px
- Ideal for: Standard desktop interfaces, main theme toggles

### Large (`size="lg"`)
- Base size: 40px
- Ideal for: Prominent placement, accessibility-focused designs

## Advanced Usage

### Integration with Next.js Themes

```tsx
import { useTheme } from "next-themes";
import { ThemeSwitch } from "@/components/ui/theme-switch";

function ThemeToggle() {
  const { theme, setTheme } = useTheme();
  
  return (
    <ThemeSwitch
      checked={theme === "dark"}
      onChange={(checked) => setTheme(checked ? "dark" : "light")}
      aria-label="Toggle dark mode"
    />
  );
}
```

### Custom Hook Integration

```tsx
import { useState, useEffect } from "react";
import { ThemeSwitch } from "@/components/ui/theme-switch";

function useThemeSwitch(defaultTheme = "light") {
  const [theme, setTheme] = useState(defaultTheme);
  
  useEffect(() => {
    document.documentElement.classList.toggle("dark", theme === "dark");
    localStorage.setItem("theme", theme);
  }, [theme]);
  
  const toggleTheme = (checked: boolean) => {
    setTheme(checked ? "dark" : "light");
  };
  
  return { theme, toggleTheme };
}

function App() {
  const { theme, toggleTheme } = useThemeSwitch();
  
  return (
    <ThemeSwitch
      checked={theme === "dark"}
      onChange={toggleTheme}
    />
  );
}
```

## Accessibility

The ThemeSwitch component is fully accessible and includes:

- **ARIA Support**: Proper `role="switch"` and `aria-checked` attributes
- **Keyboard Navigation**: Space and Enter keys to toggle
- **Focus Management**: Visible focus indicators
- **Screen Reader Support**: Descriptive labels and state announcements
- **High Contrast**: Works with high contrast mode
- **Reduced Motion**: Respects `prefers-reduced-motion` settings

### Keyboard Shortcuts

- **Tab**: Focus the switch
- **Space/Enter**: Toggle the switch state
- **Escape**: Remove focus (standard browser behavior)

## Styling

The component uses CSS-in-JS with styled-jsx for complete style encapsulation. All styles are scoped and won't conflict with your existing CSS.

### Custom Properties

The component uses CSS custom properties for theming:

```css
--toggle-size: 30px;
--container-light-bg: #3D7EAE;
--container-night-bg: #1D1F2C;
--sun-bg: #ECCA2F;
--moon-bg: #C4C9D1;
--clouds-color: #F3FDFF;
--stars-color: #fff;
```

## Performance

- **React.memo**: Prevents unnecessary re-renders
- **Efficient Animations**: CSS-based animations for smooth performance
- **Minimal Bundle Impact**: Lightweight implementation
- **Tree Shaking**: Only imports what you use

## Browser Support

- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **CSS Features**: Supports all modern CSS features used
- **Fallbacks**: Graceful degradation for older browsers

## Examples

Visit `/theme-switch-demo` to see live examples and interactive demonstrations of all features and configurations.
