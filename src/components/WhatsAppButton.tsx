"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { WHATSAPP_NUMBER, WHATSAPP_DEFAULT_MESSAGE } from "@/lib/config";
import WhatsAppIcon from "@/components/icons/WhatsAppIcon";
import { cn } from "@/lib/utils";

const encodedMessage = encodeURIComponent(WHATSAPP_DEFAULT_MESSAGE);
const whatsappUrl: string = `https://wa.me/${WHATSAPP_NUMBER}?text=${encodeURIComponent(encodedMessage)}`;

export default function WhatsAppButton(): React.JSX.Element {
    return (
        <Button
            size="icon"
            asChild
            className={cn(
                "w-12 h-12 md:w-16 md:h-16",
                "bg-[#25d366]",
                "rounded-[21%_9%_21%_9%_/_21%_9%_21%_9%]",
                "hover:bg-[#128747]",
                "shadow-lg hover:scale-110 transition-transform duration-200",
                "drop-shadow-[2px_3px_1px_#1f1f1f]",
            )}
            aria-label="Contact on WhatsApp"
        >
            <a href={whatsappUrl} target="_blank" rel="noopener noreferrer">
                <WhatsAppIcon className="scale-[3.1] md:scale-[4]" />
            </a>
        </Button>
    );
}
